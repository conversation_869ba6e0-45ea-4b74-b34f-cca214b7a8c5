#![allow(non_camel_case_types)]
#![allow(non_upper_case_globals)]
#![allow(dead_code)]

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 地图单个字段查询结果
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ditu_ziduan_jieguo {
    /// 地图ID
    pub ditu_id: String,
    /// 字段名
    pub ziduan_ming: String,
    /// 字段值
    pub ziduan_zhi: Option<String>,
}

/// 地图全部信息查询结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_quanbu_xinxi_jieguo {
    /// 地图ID
    pub ditu_id: String,
    /// 所有字段数据（包含huizong表和name表的合并数据）
    pub suoyou_ziduan: HashMap<String, String>,
    /// 数据来源（"缓存" 或 "数据库"）
    pub shuju_laiyuan: String,
}

/// 地图缓存操作结果
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ditu_huancun_caozuo_jieguo {
    /// 地图ID
    pub ditu_id: String,
    /// 操作是否成功
    pub chenggong: bool,
    /// 操作消息
    pub xiaoxi: String,
}

/// 地图数据查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_chaxun_canshu {
    /// 地图ID
    pub ditu_id: String,
    /// 字段名（如果是"quanbu_xinxi"则获取全部信息）
    pub ziduan_ming: String,
}

impl ditu_ziduan_jieguo {
    /// 创建新的地图字段查询结果
    pub fn new(ditu_id: String, ziduan_ming: String, ziduan_zhi: Option<String>) -> Self {
        Self {
            ditu_id,
            ziduan_ming,
            ziduan_zhi,
        }
    }

    /// 检查字段值是否存在
    pub fn you_zhi(&self) -> bool {
        self.ziduan_zhi.is_some()
    }
}

impl ditu_quanbu_xinxi_jieguo {
    /// 创建新的地图全部信息查询结果
    pub fn new(ditu_id: String, suoyou_ziduan: HashMap<String, String>, shuju_laiyuan: String) -> Self {
        Self {
            ditu_id,
            suoyou_ziduan,
            shuju_laiyuan,
        }
    }

    /// 获取指定字段的值
    pub fn huoqu_ziduan_zhi(&self, ziduan_ming: &str) -> Option<&String> {
        self.suoyou_ziduan.get(ziduan_ming)
    }

    /// 获取字段数量
    pub fn ziduan_shuliang(&self) -> usize {
        self.suoyou_ziduan.len()
    }

    /// 检查是否来自缓存
    pub fn laizi_huancun(&self) -> bool {
        self.shuju_laiyuan == "缓存"
    }
}

impl ditu_huancun_caozuo_jieguo {
    /// 创建成功的缓存操作结果
    pub fn chenggong(ditu_id: String, xiaoxi: String) -> Self {
        Self {
            ditu_id,
            chenggong: true,
            xiaoxi,
        }
    }

    /// 创建失败的缓存操作结果
    pub fn shibai(ditu_id: String, xiaoxi: String) -> Self {
        Self {
            ditu_id,
            chenggong: false,
            xiaoxi,
        }
    }
}

impl ditu_chaxun_canshu {
    /// 创建新的地图查询参数
    pub fn new(ditu_id: String, ziduan_ming: String) -> Self {
        Self {
            ditu_id,
            ziduan_ming,
        }
    }

    /// 检查是否是获取全部信息的查询
    pub fn shi_quanbu_xinxi_chaxun(&self) -> bool {
        self.ziduan_ming == "quanbu_xinxi"
    }
}

/// 地图列表项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_liebiao_xiangmu {
    /// 地图ID
    pub ditu_id: String,
    /// 地图名字
    pub ditu_mingzi: String,
    /// 地图分类（包含所有为true的分类字段）
    pub ditu_fenlei: HashMap<String, bool>,
}

/// 地图列表查询参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_liebiao_chaxun_canshu {
    /// 每页数量
    pub mei_ye_shuliang: u32,
    /// 当前页数（从1开始）
    pub dangqian_ye: u32,
}

/// 地图列表查询结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ditu_liebiao_jieguo {
    /// 地图列表
    pub ditu_liebiao: Vec<ditu_liebiao_xiangmu>,
    /// 当前页数
    pub dangqian_ye: u32,
    /// 全部页数
    pub quanbu_ye_shu: u32,
    /// 总共地图数量
    pub zonggong_ditu_shuliang: u32,
    /// 当前页面地图数量
    pub dangqian_ye_ditu_shuliang: u32,
}

impl ditu_liebiao_xiangmu {
    /// 创建新的地图列表项
    pub fn new(ditu_id: String, ditu_mingzi: String, ditu_fenlei: HashMap<String, bool>) -> Self {
        Self {
            ditu_id,
            ditu_mingzi,
            ditu_fenlei,
        }
    }
}

impl ditu_liebiao_chaxun_canshu {
    /// 创建新的地图列表查询参数
    pub fn new(mei_ye_shuliang: u32, dangqian_ye: u32) -> Self {
        Self {
            mei_ye_shuliang,
            dangqian_ye,
        }
    }

    /// 计算OFFSET值
    pub fn jisuan_offset(&self) -> u32 {
        (self.dangqian_ye - 1) * self.mei_ye_shuliang
    }
}

impl ditu_liebiao_jieguo {
    /// 创建新的地图列表查询结果
    pub fn new(
        ditu_liebiao: Vec<ditu_liebiao_xiangmu>,
        dangqian_ye: u32,
        zonggong_ditu_shuliang: u32,
        mei_ye_shuliang: u32,
    ) -> Self {
        let dangqian_ye_ditu_shuliang = ditu_liebiao.len() as u32;
        let quanbu_ye_shu = if zonggong_ditu_shuliang == 0 {
            0
        } else {
            (zonggong_ditu_shuliang + mei_ye_shuliang - 1) / mei_ye_shuliang
        };

        Self {
            ditu_liebiao,
            dangqian_ye,
            quanbu_ye_shu,
            zonggong_ditu_shuliang,
            dangqian_ye_ditu_shuliang,
        }
    }
}